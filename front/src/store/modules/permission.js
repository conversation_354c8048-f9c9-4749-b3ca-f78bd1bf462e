import { defineStore } from 'pinia'
import { ref } from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import { getRouters } from '@/api/auth'
import Layout from '@/layouts/index.vue'
import router, { defaultRouterList } from '@/router'
import { isHttp } from '@/utils/validate'

// 自动导入views文件夹下所有vue文件
const modules = import.meta.glob('../../views/**/*.vue')

export const usePermissionStore = defineStore('permission', () => {
  const whiteListRouters = ref([
    '/login',
    '/register',
    '/contact',
    '/403',
    '/500',
    '/404',
    '/test'
  ])
  const menus = ref([])
  const allMenus = ref([])

  async function generateRoutes() {
    try {
      console.log('🚀 开始生成动态路由...')
      console.log('📊 当前menus状态:', menus.value.length > 0 ? '已有数据' : '空')

      // 如果已经有菜单数据，直接返回，避免重复生成
      if (menus.value.length > 0) {
        console.log('⚠️ 路由已存在，跳过重复生成')
        return
      }

      // 向后端请求路由数据
      const res = await getRouters()
      console.log('📡 getRouters响应:', res.data)

      const asyncRouter = filterAsyncRouter(cloneDeep(res.data))
      console.log('🔧 过滤后的路由:', asyncRouter)

      menus.value = asyncRouter
      allMenus.value = defaultRouterList.concat(asyncRouter)

      // 清除之前的动态路由（防止重复添加）
      console.log('🧹 清除之前的动态路由...')

      // 根据后台路由数据生成可访问路由表
      asyncRouter.forEach((route, index) => {
        // Layout路由的path可以是空字符串，这是正常的
        if (route.path !== undefined && !isHttp(route.path)) {
          console.log(`➕ 添加动态路由 [${index}]:`, {
            path: route.path,
            name: route.name,
            component: route.component?.name || 'unknown',
            hasChildren: !!(route.children && route.children.length > 0),
            childrenCount: route.children?.length || 0
          })
          router.addRoute(route) // 动态添加可访问路由表
        }
      })

      // 打印所有已注册的路由
      const allRoutes = router.getRoutes()
      console.log('📋 所有已注册路由:', allRoutes.map(r => ({
        path: r.path,
        name: r.name,
        component: r.component?.name || 'unknown'
      })))

      console.log('✅ 动态路由生成完成')
    } catch (error) {
      console.error('❌ 生成路由失败:', error)
      throw error
    }
  }

  // 检查路径是否在白名单中
  const isWhiteList = (path) => {
    return whiteListRouters.value.some((pattern) => {
      if (pattern === path) return true
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*')
        const regex = new RegExp(`^${regexPattern}$`)
        return regex.test(path)
      }
      return false
    })
  }

  // 清空路由
  const clearRoutes = () => {
    menus.value = []
    allMenus.value = [...defaultRouterList]
  }

  // 获取菜单树
  const getMenuTree = () => {
    return menus.value
  }

  return {
    menus,
    allMenus,
    whiteListRouters,
    generateRoutes,
    isWhiteList,
    clearRoutes,
    getMenuTree,
  }
})

/**
 * 遍历后台传来的路由字符串，转换为组件对象
 * @param routers 后台传来的路由字符串
 */
function filterAsyncRouter(routers) {
  console.log('🔧 filterAsyncRouter 输入:', routers.map(r => ({
    title: r.title,
    path: r.path,
    component: r.component,
    hasChildren: !!(r.children && r.children.length > 0),
    parent_id: r.parent_id
  })))

  // 检查是否已经是树形结构（有父子关系）
  const hasTreeStructure = routers.some(r => r.children && r.children.length > 0)
  const hasLayoutComponent = routers.some(r => {
    const componentStr = r.component?.toString() || r.component
    return componentStr === 'Layout'
  })

  console.log('🔍 结构检查:', {
    hasTreeStructure,
    hasLayoutComponent,
    routersCount: routers.length,
    components: routers.map(r => r.component)
  })

  // 如果后端返回的是扁平结构的菜单，需要包装在Layout中
  if (routers.length > 0 && !hasTreeStructure && !hasLayoutComponent) {
    console.log('📦 检测到扁平结构，创建Layout包装器')
    // 创建Layout包装器
    const layoutRoute = {
      path: '',
      component: Layout,
      redirect: routers[0] ? componentToPath(routers[0].component) : '/home',
      children: []
    }

    // 处理每个菜单项
    routers.forEach((route) => {
      const processedRoute = processMenuRoute(route)
      if (processedRoute) {
        layoutRoute.children.push(processedRoute)
      }
    })

    return [layoutRoute]
  }

  // 如果已经有Layout结构或树形结构，按原逻辑处理
  console.log('🏗️ 检测到树形结构，按原逻辑处理')
  return routers.map((route) => {
    console.log(`🔧 处理路由: ${route.title} (${route.component})`)

    // 处理当前路由
    processMenuRoute(route)

    // 递归处理子路由
    if (route.children?.length) {
      console.log(`📁 处理子路由: ${route.title} 有 ${route.children.length} 个子路由`)
      route.children = filterAsyncRouter(route.children)
    } else {
      delete route.children
      delete route.redirect
    }
    return route
  })
}

/**
 * 处理单个菜单路由
 */
function processMenuRoute(route) {
  console.log(`🔧 processMenuRoute 开始处理: ${route.title}`, {
    originalPath: route.path,
    component: route.component,
    hasChildren: !!(route.children && route.children.length > 0)
  })

  // 处理路径：优先使用数据库中的path字段，否则根据component生成
  if (route.path) {
    // 对于Layout组件（父级菜单），确保path以斜杠开头
    if (route.component?.toString() === 'Layout') {
      if (!route.path.startsWith('/')) {
        route.path = '/' + route.path
      }
      console.log(`📍 Layout路由path: ${route.title} -> ${route.path}`)
    } else {
      // 对于子级路由，如果有父级，则保持相对路径；否则确保以斜杠开头
      // 这里我们需要检查是否是子路由（通过parent_id或其他方式）
      // 暂时保持原有逻辑，确保非Layout路由的path处理
      console.log(`📍 子级路由path: ${route.title} -> ${route.path} (保持原样)`)
    }
  } else if (route.component) {
    // 根据component生成path
    route.path = componentToPath(route.component)
    console.log(`🔧 根据component生成path: ${route.title} -> ${route.path}`)
  }

  // 构建meta对象
  route.meta = {
    title: route.title,
    icon: route.icon,
    hidden: route.is_hidden === 1,
    cache: route.is_cache === 1
  }

  if (route.component) {
    // Layout组件特殊处理 - 更严格的判断
    const componentStr = route.component?.toString() || route.component
    if (componentStr === 'Layout') {
      route.component = Layout
      // Layout路由通常不需要name，但为了调试方便可以设置
      // 如果有多个Layout路由，可以基于path生成唯一name
      if (route.path && route.path !== '') {
        route.name = route.path.replace(/\//g, '') || 'layout'
        console.log(`🏷️ Layout路由设置name: ${route.title} -> ${route.name}`)
      } else {
        console.log(`🏷️ Layout路由无path，不设置name: ${route.title}`)
      }
    } else {
      // 设置name（用于路由缓存）- 基于component字段生成小写开头的驼峰形式
      // 例如: Index/index -> index, GetMailbox/index -> getMailbox
      let componentName = route.component.replace(/\/index$/, '') // 移除/index后缀
      componentName = componentName.replace(/\//g, '') // 移除斜杠
      // 将首字母转换为小写
      route.name = componentName.charAt(0).toLowerCase() + componentName.slice(1)
      console.log(`🏷️ 普通路由设置name: ${route.title} -> ${route.name}`)

      route.component = loadView(route.component)
    }
  }

  console.log(`✅ processMenuRoute 完成: ${route.title}`, {
    finalPath: route.path,
    finalName: route.name,
    componentType: route.component?.name || 'unknown'
  })

  return route
}

/**
 * 根据组件路径生成路由路径
 * @param component 组件路径，如 'Home/index' 或 'GetMailbox/index'
 */
function componentToPath(component) {
  if (!component) return '/'

  // 移除 /index 后缀
  let cleanComponent = component.replace(/\/index$/, '')

  // 直接使用组件路径作为URL路径
  const path = '/' + cleanComponent

  return path
}

/**
 * 加载组件
 * @param view 组件名称，如 'Home/index'
 */
export const loadView = (view) => {
  // 构建完整的路径
  const fullPath = `../../views/${view}.vue`

  // 查找匹配的模块
  for (const path in modules) {
    if (path === fullPath) {
      return () => modules[path]()
    }
  }

  // 如果没找到，尝试其他可能的路径格式
  for (const path in modules) {
    const dir = path.split('views/')[1]?.split('.vue')[0]
    if (dir === view) {
      return () => modules[path]()
    }
  }

  console.error(`无法找到组件: ${view}`)
  return null
}


