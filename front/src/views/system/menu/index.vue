<template>
  <div class="menu-management">
    <div class="page-header">
      <h1 class="page-title">菜单管理</h1>
      <p class="page-description">管理系统菜单结构，配置菜单权限和显示设置</p>
    </div>

    <div class="page-content">
      <!-- 操作栏 -->
      <div class="action-bar">
        <t-button theme="primary" @click="handleAdd">
          <template #icon>
            <Plus :size="16" />
          </template>
          新增菜单
        </t-button>
        <t-button variant="outline" @click="handleRefresh">
          <template #icon>
            <RefreshCw :size="16" />
          </template>
          刷新
        </t-button>
      </div>

      <!-- 菜单树表格 -->
      <div class="table-container">
        <t-table
          :data="menuData"
          :columns="columns"
          :tree="treeConfig"
          :loading="loading"
          row-key="id"
          :pagination="false"
          stripe
          hover
        >
          <!-- 菜单名称列 -->
          <template #name="{ row }">
            <div class="menu-name-cell">
              <component 
                v-if="row.icon" 
                :is="getIconComponent(row.icon)" 
                :size="16" 
                class="menu-icon"
              />
              <span>{{ row.title }}</span>
              <t-tag v-if="row.parent_id === null" theme="primary" variant="light" size="small">
                父级
              </t-tag>
            </div>
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </t-tag>
          </template>

          <!-- 隐藏状态列 -->
          <template #hidden="{ row }">
            <t-tag :theme="row.is_hidden === 0 ? 'success' : 'warning'" variant="light">
              {{ row.is_hidden === 0 ? '显示' : '隐藏' }}
            </t-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <t-space>
              <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                编辑
              </t-button>
              <t-button theme="success" variant="text" size="small" @click="handleAddChild(row)">
                新增子菜单
              </t-button>
              <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                删除
              </t-button>
            </t-space>
          </template>
        </t-table>
      </div>
    </div>

    <!-- 新增/编辑菜单对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      width="600px"
      :confirm-btn="{ content: '确定', loading: submitLoading }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <t-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="父级菜单" name="parent_id">
          <t-select
            v-model="formData.parent_id"
            placeholder="请选择父级菜单（不选择则为顶级菜单）"
            clearable
          >
            <t-option
              v-for="menu in parentMenuOptions"
              :key="menu.id"
              :value="menu.id"
              :label="menu.title"
            />
          </t-select>
        </t-form-item>

        <t-form-item label="菜单名称" name="name">
          <t-input
            v-model="formData.name"
            placeholder="请输入菜单名称（英文标识）"
          />
        </t-form-item>

        <t-form-item label="菜单标题" name="title">
          <t-input
            v-model="formData.title"
            placeholder="请输入菜单标题（中文显示名称）"
          />
        </t-form-item>

        <t-form-item label="组件路径" name="component">
          <t-input
            v-model="formData.component"
            placeholder="请输入组件路径，如：system/menu/index"
          />
        </t-form-item>

        <t-form-item label="菜单图标" name="icon">
          <t-input
            v-model="formData.icon"
            placeholder="请输入图标名称，如：menu"
          />
        </t-form-item>

        <t-form-item label="排序顺序" name="sort_order">
          <t-input-number
            v-model="formData.sort_order"
            :min="0"
            placeholder="请输入排序顺序"
          />
        </t-form-item>

        <t-form-item label="是否隐藏" name="is_hidden">
          <t-radio-group v-model="formData.is_hidden">
            <t-radio :value="0">显示</t-radio>
            <t-radio :value="1">隐藏</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="是否缓存" name="is_cache">
          <t-radio-group v-model="formData.is_cache">
            <t-radio :value="1">缓存</t-radio>
            <t-radio :value="0">不缓存</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio :value="1">启用</t-radio>
            <t-radio :value="0">禁用</t-radio>
          </t-radio-group>
        </t-form-item>

        <t-form-item label="备注" name="remark">
          <t-textarea
            v-model="formData.remark"
            placeholder="请输入备注信息"
            :maxlength="500"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw, Menu, Settings, Home, User, Mail } from 'lucide-vue-next'

// 响应式数据
const loading = ref(false)
const menuData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)

// 表单数据
const formData = reactive({
  parent_id: null,
  name: '',
  title: '',
  component: '',
  icon: '',
  sort_order: 0,
  is_hidden: 0,
  is_cache: 1,
  status: 1,
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入菜单名称', type: 'error' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '菜单名称只能包含字母、数字和下划线，且以字母开头', type: 'error' }
  ],
  title: [
    { required: true, message: '请输入菜单标题', type: 'error' }
  ],
  component: [
    { required: true, message: '请输入组件路径', type: 'error' }
  ]
}

// 表格配置
const treeConfig = {
  childrenKey: 'children',
  treeNodeColumnIndex: 0
}

// 表格列配置
const columns = [
  {
    colKey: 'title',
    title: '菜单名称',
    width: 200,
    ellipsis: true,
    cell: 'name'
  },
  {
    colKey: 'name',
    title: '标识名称',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'component',
    title: '组件路径',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'icon',
    title: '图标',
    width: 100
  },
  {
    colKey: 'sort_order',
    title: '排序',
    width: 80
  },
  {
    colKey: 'status',
    title: '状态',
    width: 80,
    cell: 'status'
  },
  {
    colKey: 'is_hidden',
    title: '显示状态',
    width: 100,
    cell: 'hidden'
  },
  {
    colKey: 'action',
    title: '操作',
    width: 200,
    cell: 'action'
  }
]

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑菜单' : '新增菜单'
})

const parentMenuOptions = computed(() => {
  // 获取所有父级菜单（没有parent_id的菜单）
  return menuData.value.filter(menu => menu.parent_id === null)
})

// 图标组件映射
const iconComponents = {
  menu: Menu,
  setting: Settings,
  home: Home,
  user: User,
  mail: Mail
}

const getIconComponent = (iconName) => {
  return iconComponents[iconName] || Menu
}

// 方法
const loadMenuData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取菜单数据
    // const response = await api.get('/api/menus')
    // menuData.value = response.data
    
    // 模拟数据
    menuData.value = [
      {
        id: 'a8f5f167-7c4a-4b2e-9d1f-3e8b9c5a7f2d',
        parent_id: null,
        name: 'System',
        title: '系统管理',
        component: 'Layout',
        icon: 'setting',
        sort_order: 100,
        is_hidden: 0,
        is_cache: 1,
        status: 1,
        remark: '系统管理模块',
        children: [
          {
            id: 'b2e8c9f4-1a6d-4e3b-8c7f-9d5e2a4b6c8f',
            parent_id: 'a8f5f167-7c4a-4b2e-9d1f-3e8b9c5a7f2d',
            name: 'MenuManagement',
            title: '菜单管理',
            component: 'system/menu/index',
            icon: 'menu',
            sort_order: 1,
            is_hidden: 0,
            is_cache: 1,
            status: 1,
            remark: '菜单管理功能'
          }
        ]
      }
    ]
  } catch (error) {
    console.error('加载菜单数据失败:', error)
    MessagePlugin.error('加载菜单数据失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  resetForm()
  editingId.value = null
  dialogVisible.value = true
}

const handleAddChild = (row) => {
  resetForm()
  formData.parent_id = row.id
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, row)
  editingId.value = row.id
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  // TODO: 实现删除功能
  MessagePlugin.info('删除功能待实现')
}

const handleRefresh = () => {
  loadMenuData()
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  submitLoading.value = true
  try {
    if (editingId.value) {
      // TODO: 调用编辑API
      MessagePlugin.success('菜单更新成功')
    } else {
      // TODO: 调用新增API
      MessagePlugin.success('菜单创建成功')
    }
    
    dialogVisible.value = false
    loadMenuData()
  } catch (error) {
    console.error('提交失败:', error)
    MessagePlugin.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    parent_id: null,
    name: '',
    title: '',
    component: '',
    icon: '',
    sort_order: 0,
    is_hidden: 0,
    is_cache: 1,
    status: 1,
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 生命周期
onMounted(() => {
  loadMenuData()
})
</script>

<style lang="less" scoped>
.menu-management {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 8px 0;
  }
  
  .page-description {
    color: #6b7280;
    margin: 0;
  }
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .menu-icon {
    color: #6b7280;
  }
}
</style>
